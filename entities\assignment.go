package entities

import (
	"eddyowl-backend/constants"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// Assignment represents an assignment with multiple questions.
type Assignment struct {
	ID          *string          `json:"id,omitempty" bson:"_id,omitempty"`
	InstituteID *string          `json:"institute_id,omitempty" bson:"institute_id,omitempty"`
	Name        *string          `json:"name" bson:"name"`
	TermID      *string          `json:"term_id,omitempty" bson:"term_id,omitempty"`
	Grade       int              `json:"grade" bson:"grade"`
	Subject     *string          `json:"subject" bson:"subject"`
	Sections    *[]string        `json:"sections" bson:"sections"`
	History     *[]StatusHistory `json:"history" bson:"history"`
	Deadline    *time.Time       `json:"deadline" bson:"deadline"`
	TotalScore  float32          `json:"total_score" bson:"total_score"`
	Questions   *[]Question      `json:"questions" bson:"questions"`
	FolderID    *string          `json:"folder_id,omitempty" bson:"folder_id,omitempty"`
	CreatedBy   *string          `json:"created_by" bson:"created_by"`
	CreatedAt   *time.Time       `json:"created_at" bson:"created_at"`
	UpdatedBy   *string          `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	UpdatedAt   *time.Time       `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	DeletedBy   *string          `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`
	DeletedAt   *time.Time       `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
}

// Question represents a single question in the assignment.
type Question struct {
	Question       *string  `json:"question" bson:"question"`
	Rubric         *string  `json:"rubric" bson:"rubric"`
	Score          float32  `json:"score" bson:"score"`
	Topics         *[]Topic `json:"topics" bson:"topics"`
	QuestionNumber int      `json:"question_number" bson:"question_number"`
}

// Topic represents a set of topic keywords or subtopics (e.g., in a chapter).
type Topic struct {
	Chapter *string   `json:"chapter" bson:"chapter"`
	Topics  *[]string `json:"topics" bson:"topics"`
}

// NewAssignment initializes a new assignment.
func NewAssignment(instituteId, name, termID, subject string, grade int, sections []string, deadline *time.Time, totalScore float32, questions []Question, createdBy string, folderId *string) *Assignment {
	id := uuid.New().String()
	now := time.Now()
	return &Assignment{
		InstituteID: &instituteId,
		ID:          &id,
		Name:        &name,
		TermID:      &termID,
		Grade:       grade,
		Subject:     &subject,
		Sections:    &sections,
		Deadline:    deadline,
		TotalScore:  totalScore,
		Questions:   &questions,
		FolderID:    folderId,
		CreatedBy:   &createdBy,
		CreatedAt:   &now,
	}
}

// Validate ensures Assignment data is correct.
func (a *Assignment) Validate() error {
	if a.InstituteID == nil || *a.InstituteID == "" {
		return errors.New("institute ID is required")
	}
	if a.Name == nil || *a.Name == "" {
		return errors.New("assignment name is required")
	}
	if a.TermID == nil || *a.TermID == "" {
		return errors.New("term ID is required")
	}
	if a.Subject == nil || *a.Subject == "" {
		return errors.New("subject is required")
	}
	if a.Grade < constants.MinSchoolGrade || a.Grade > constants.MaxSchoolGrade {
		return fmt.Errorf("grade must be between %d and %d; got %d",
			constants.MinSchoolGrade, constants.MaxSchoolGrade, a.Grade)
	}
	if a.Sections == nil || len(*a.Sections) == 0 {
		return errors.New("at least one section is required")
	}
	if a.TotalScore <= 0 {
		return errors.New("total score must be greater than zero")
	}
	if a.Questions == nil || len(*a.Questions) == 0 {
		return errors.New("at least one question is required")
	}
	for _, q := range *a.Questions {
		if q.Question == nil || *q.Question == "" {
			return errors.New("question text is required")
		}
		if q.Score <= 0 {
			return errors.New("question score must be greater than zero")
		}
		if q.QuestionNumber <= 0 {
			return errors.New("question number must be greater than zero")
		}
	}
	return nil
}

// NewQuestion initializes a new question.
func NewQuestion(questionNumber int, question, rubric string, score float32, topics []Topic) *Question {
	return &Question{
		Question:       &question,
		Rubric:         &rubric,
		Score:          score,
		Topics:         &topics,
		QuestionNumber: questionNumber,
	}
}

// Validate ensures Question data is correct.
func (q *Question) Validate() error {
	if q.Question == nil || *q.Question == "" {
		return errors.New("question text is required")
	}
	if q.Rubric == nil || *q.Rubric == "" {
		return errors.New("rubric is required")
	}
	if q.Score <= 0 {
		return errors.New("question score must be greater than zero")
	}
	if q.Topics == nil || len(*q.Topics) == 0 {
		return errors.New("at least one topic is required")
	}
	if q.QuestionNumber <= 0 {
		return errors.New("question number must be greater than zero")
	}
	return nil
}

// NewTopic initializes a new topic.
func NewTopic(chapter string, topics []string) *Topic {
	return &Topic{
		Chapter: &chapter,
		Topics:  &topics,
	}
}

// Validate ensures Topic data is correct.
func (t *Topic) Validate() error {
	if t.Chapter == nil || *t.Chapter == "" {
		return errors.New("chapter is required")
	}
	if t.Topics == nil || len(*t.Topics) == 0 {
		return errors.New("at least one topic is required")
	}
	return nil
}

// AddStatusHistory appends a new assignment history record.
func (a *Assignment) AddStatusHistory(status int, updatedBy string) error {
	newHistory := NewStatusHistory(status, updatedBy)
	err := newHistory.Validate()
	if err != nil {
		return err
	}
	if a.History == nil {
		a.History = &[]StatusHistory{}
	}
	*a.History = append(*a.History, *newHistory)
	return nil
}
