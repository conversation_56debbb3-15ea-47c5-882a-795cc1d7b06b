// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetAllAssignmentsParams creates a new GetAllAssignmentsParams object
//
// There are no default values defined in the spec.
func NewGetAllAssignmentsParams() GetAllAssignmentsParams {

	return GetAllAssignmentsParams{}
}

// GetAllAssignmentsParams contains all the bound params for the get all assignments operation
// typically these are obtained from a http.Request
//
// swagger:parameters GetAllAssignments
type GetAllAssignmentsParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*Return only assignments in this folder
	  In: query
	*/
	FolderID *string
	/*
	  In: query
	*/
	Grade *int32
	/*
	  Required: true
	  In: path
	*/
	InstituteID string
	/*
	  In: query
	*/
	Section []string
	/*
	  In: query
	*/
	Subject *string
	/*
	  In: query
	*/
	TermID *string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewGetAllAssignmentsParams() beforehand.
func (o *GetAllAssignmentsParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	qs := runtime.Values(r.URL.Query())

	qFolderID, qhkFolderID, _ := qs.GetOK("folderId")
	if err := o.bindFolderID(qFolderID, qhkFolderID, route.Formats); err != nil {
		res = append(res, err)
	}

	qGrade, qhkGrade, _ := qs.GetOK("grade")
	if err := o.bindGrade(qGrade, qhkGrade, route.Formats); err != nil {
		res = append(res, err)
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}

	qSection, qhkSection, _ := qs.GetOK("section")
	if err := o.bindSection(qSection, qhkSection, route.Formats); err != nil {
		res = append(res, err)
	}

	qSubject, qhkSubject, _ := qs.GetOK("subject")
	if err := o.bindSubject(qSubject, qhkSubject, route.Formats); err != nil {
		res = append(res, err)
	}

	qTermID, qhkTermID, _ := qs.GetOK("termId")
	if err := o.bindTermID(qTermID, qhkTermID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindFolderID binds and validates parameter FolderID from query.
func (o *GetAllAssignmentsParams) bindFolderID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.FolderID = &raw

	return nil
}

// bindGrade binds and validates parameter Grade from query.
func (o *GetAllAssignmentsParams) bindGrade(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}

	value, err := swag.ConvertInt32(raw)
	if err != nil {
		return errors.InvalidType("grade", "query", "int32", raw)
	}
	o.Grade = &value

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *GetAllAssignmentsParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}

// bindSection binds and validates array parameter Section from query.
//
// Arrays are parsed according to CollectionFormat: "" (defaults to "csv" when empty).
func (o *GetAllAssignmentsParams) bindSection(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var qvSection string
	if len(rawData) > 0 {
		qvSection = rawData[len(rawData)-1]
	}

	// CollectionFormat:
	sectionIC := swag.SplitByFormat(qvSection, "")
	if len(sectionIC) == 0 {
		return nil
	}

	var sectionIR []string
	for _, sectionIV := range sectionIC {
		sectionI := sectionIV

		sectionIR = append(sectionIR, sectionI)
	}

	o.Section = sectionIR

	return nil
}

// bindSubject binds and validates parameter Subject from query.
func (o *GetAllAssignmentsParams) bindSubject(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.Subject = &raw

	return nil
}

// bindTermID binds and validates parameter TermID from query.
func (o *GetAllAssignmentsParams) bindTermID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.TermID = &raw

	return nil
}
