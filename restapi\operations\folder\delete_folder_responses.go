// Code generated by go-swagger; DO NOT EDIT.

package folder

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// DeleteFolderOKCode is the HTTP code returned for type DeleteFolderOK
const DeleteFolderOKCode int = 200

/*
DeleteFolderOK Folder deleted

swagger:response deleteFolderOK
*/
type DeleteFolderOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewDeleteFolderOK creates DeleteFolderOK with default headers values
func NewDeleteFolderOK() *DeleteFolderOK {

	return &DeleteFolderOK{}
}

// WithPayload adds the payload to the delete folder o k response
func (o *DeleteFolderOK) WithPayload(payload *models.SuccessResponse) *DeleteFolderOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete folder o k response
func (o *DeleteFolderOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteFolderOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// DeleteFolderBadRequestCode is the HTTP code returned for type DeleteFolderBadRequest
const DeleteFolderBadRequestCode int = 400

/*
DeleteFolderBadRequest Bad Request

swagger:response deleteFolderBadRequest
*/
type DeleteFolderBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteFolderBadRequest creates DeleteFolderBadRequest with default headers values
func NewDeleteFolderBadRequest() *DeleteFolderBadRequest {

	return &DeleteFolderBadRequest{}
}

// WithPayload adds the payload to the delete folder bad request response
func (o *DeleteFolderBadRequest) WithPayload(payload models.ErrorResponse) *DeleteFolderBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete folder bad request response
func (o *DeleteFolderBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteFolderBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
