package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
	"time"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type folderProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

// Create implements FolderProvider.
func (f *folderProvider) Create(ctx context.Context, folder *entities.Folder) error {
	ctx, span := f.tracer.Start(ctx, "FolderProvider : Create")
	defer span.End()

	collection := f.mongoClient.Database(f.dbName).Collection(constants.MongoDBCollectionFolders)
	_, err := collection.InsertOne(ctx, folder)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create folder")
		return err
	}
	return nil
}

// GetAll implements FolderProvider.
func (f *folderProvider) GetAll(ctx context.Context, instituteID string) (*[]entities.Folder, error) {
	ctx, span := f.tracer.Start(ctx, "FolderProvider : GetAll")
	defer span.End()

	collection := f.mongoClient.Database(f.dbName).Collection(constants.MongoDBCollectionFolders)
	filter := bson.D{
		{"institute_id", instituteID},
		{"deleted_at", bson.D{{"$exists", false}}},
	}

	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get folders")
		return nil, err
	}
	defer cursor.Close(ctx)

	var folders []entities.Folder
	err = cursor.All(ctx, &folders)
	if err != nil {
		log.Error().Err(err).Msg("Failed to decode folders")
		return nil, err
	}

	return &folders, nil
}

// GetByID implements FolderProvider.
func (f *folderProvider) GetByID(ctx context.Context, folderID string) (*entities.Folder, error) {
	ctx, span := f.tracer.Start(ctx, "FolderProvider : GetByID")
	defer span.End()

	collection := f.mongoClient.Database(f.dbName).Collection(constants.MongoDBCollectionFolders)
	filter := bson.D{
		{"_id", folderID},
		{"deleted_at", bson.D{{"$exists", false}}},
	}

	var folder entities.Folder
	err := collection.FindOne(ctx, filter).Decode(&folder)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get folder by ID")
		return nil, err
	}

	return &folder, nil
}

// Update implements FolderProvider.
func (f *folderProvider) Update(ctx context.Context, folder *entities.Folder) error {
	ctx, span := f.tracer.Start(ctx, "FolderProvider : Update")
	defer span.End()

	collection := f.mongoClient.Database(f.dbName).Collection(constants.MongoDBCollectionFolders)
	filter := bson.D{
		{"_id", *folder.ID},
		{"deleted_at", bson.D{{"$exists", false}}},
	}

	now := time.Now()
	update := bson.D{
		{"$set", bson.D{
			{"name", folder.Name},
			{"updated_by", folder.UpdatedBy},
			{"updated_at", now},
		}},
	}

	_, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		log.Error().Err(err).Msg("Failed to update folder")
		return err
	}

	return nil
}

// Delete implements FolderProvider.
func (f *folderProvider) Delete(ctx context.Context, folderID string, deletedBy string) error {
	ctx, span := f.tracer.Start(ctx, "FolderProvider : Delete")
	defer span.End()

	// Get the folder first to save it to archive
	folder, err := f.GetByID(ctx, folderID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get folder for deletion")
		return err
	}

	// Set deletion details
	currentTime := time.Now()
	folder.DeletedAt = &currentTime
	folder.DeletedBy = &deletedBy

	// Save to archive collection
	_, err = f.mongoClient.Database(f.dbName).Collection(constants.MongoDBCollectionFoldersArchive).InsertOne(ctx, folder)
	if err != nil {
		log.Error().Err(err).Msg("Failed to save folder to archive")
		return err
	}

	// Delete from main collection
	res, err := f.mongoClient.Database(f.dbName).Collection(constants.MongoDBCollectionFolders).DeleteOne(ctx, bson.D{{"_id", folderID}})
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete folder from main collection")
		return err
	}

	if res.DeletedCount == 0 {
		log.Error().Str("folderID", folderID).Msg("No folder found to delete")
		return mongo.ErrNoDocuments
	}

	return nil
}

func NewFolderProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) FolderProvider {
	return &folderProvider{
		mongoClient: mongoClient,
		dbName:      databaseName,
		tracer:      tracer,
	}
}
