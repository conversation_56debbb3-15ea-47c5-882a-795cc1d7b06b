package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
	"errors"
	"time"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type assignmentProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

// Add implements AssignmentProvider.
func (a *assignmentProvider) Add(ctx context.Context, assignment *entities.Assignment) (string, error) {
	ctx, span := a.tracer.Start(ctx, "AssignmentProvider : Add")
	defer span.End()

	if assignment.ID == nil {
		return constants.EmptyString, errors.New("assignment ID is required")
	}

	_, err := a.mongoClient.Database(a.dbName).
		Collection(constants.MongoDBCollectionAssignments).
		InsertOne(ctx, assignment)
	if err != nil {
		log.Error().Msg(err.<PERSON>rror())
		return constants.EmptyString, err
	}
	return *assignment.ID, nil
}

// Delete implements AssignmentProvider.
func (a *assignmentProvider) Delete(ctx context.Context, assignmentId string, instituteId string, deletedBy string) error {
	ctx, span := a.tracer.Start(ctx, "AssignmentProvider : Delete")
	defer span.End()
	assignment, err := a.Get(ctx, assignmentId, instituteId)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	currentTime := time.Now()
	assignment.DeletedAt = &currentTime
	assignment.DeletedBy = &deletedBy
	_, err = a.mongoClient.Database(a.dbName).Collection(constants.MongoDBCollectionAssignmentsArchive).InsertOne(ctx, assignment)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	res, err := a.mongoClient.Database(a.dbName).Collection(constants.MongoDBCollectionAssignments).DeleteOne(ctx, bson.D{{"_id", assignmentId}})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.DeletedCount != 1 {
		log.Error().Msg("Assignment not found : " + assignmentId)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Edit implements AssignmentProvider.
func (a *assignmentProvider) Edit(ctx context.Context, assignmentId string, instituteId string, assignment *entities.Assignment) error {
	ctx, span := a.tracer.Start(ctx, "AssignmentProvider : Edit")
	defer span.End()
	now := time.Now()
	assignment.UpdatedAt = &now
	res, err := a.mongoClient.Database(a.dbName).Collection(constants.MongoDBCollectionAssignments).UpdateOne(ctx, bson.D{{"_id", assignmentId}}, bson.M{"$set": assignment})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.MatchedCount != 1 {
		log.Error().Msg("Assignment not found : " + assignmentId)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Get implements AssignmentProvider.
func (a *assignmentProvider) Get(ctx context.Context, assignmentId string, instituteId string) (*entities.Assignment, error) {
	ctx, span := a.tracer.Start(ctx, "AssignmentProvider : Get")
	defer span.End()
	assignment := &entities.Assignment{}
	filter := bson.D{
		{"_id", assignmentId},
		{"institute_id", instituteId},
	}
	err := a.mongoClient.Database(a.dbName).Collection(constants.MongoDBCollectionAssignments).FindOne(ctx, filter).Decode(assignment)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return assignment, nil
}

// GetAll implements AssignmentProvider.
func (a *assignmentProvider) GetAll(ctx context.Context, instituteId string, termId *string, grade *int, sections *[]string, subject *string, folderId *string) (*[]entities.Assignment, error) {
	ctx, span := a.tracer.Start(ctx, "AssignmentProvider : GetAll")
	defer span.End()
	assignmentList := make([]entities.Assignment, 0)
	filter := bson.D{
		{"institute_id", instituteId},
	}
	if termId != nil {
		filter = append(filter, bson.E{Key: "term_id", Value: *termId})
	}
	if grade != nil {
		filter = append(filter, bson.E{Key: "grade", Value: *grade})
	}
	if sections != nil && len(*sections) > 0 {
		filter = append(filter, bson.E{Key: "sections", Value: bson.M{"$in": *sections}})
	}
	if subject != nil {
		filter = append(filter, bson.E{Key: "subject", Value: *subject})
	}
	if folderId != nil {
		filter = append(filter, bson.E{Key: "folder_id", Value: *folderId})
	}

	cursor, err := a.mongoClient.Database(a.dbName).Collection(constants.MongoDBCollectionAssignments).Find(ctx, filter)

	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}

	err = cursor.All(ctx, &assignmentList)

	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return &assignmentList, nil
}

func NewAssignmentProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) AssignmentProvider {
	return &assignmentProvider{
		mongoClient: mongoClient,
		dbName:      databaseName,
		tracer:      tracer,
	}
}
