package entities

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

var (
	ErrFolderNameEmpty = errors.New("folder name cannot be empty")
)

type Folder struct {
	ID          *string    `json:"id,omitempty" bson:"_id,omitempty"`
	InstituteID *string    `json:"institute_id" bson:"institute_id"`
	Name        *string    `json:"name" bson:"name"`
	CreatedBy   *string    `json:"created_by" bson:"created_by"`
	CreatedAt   *time.Time `json:"created_at" bson:"created_at"`
	UpdatedBy   *string    `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	UpdatedAt   *time.Time `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	DeletedBy   *string    `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
}

func NewFolder(instituteID, name string, createdBy string) *Folder {
	now := time.Now()
	id := uuid.New().String()
	folder := &Folder{
		ID:          &id,
		InstituteID: &instituteID,
		Name:        &name,
		CreatedBy:   &createdBy,
		CreatedAt: &now,
	}
	return folder
}

func (f *Folder) Validate() error {
	if f.Name == nil || *f.Name == "" {
		return ErrFolderNameEmpty
	}
	if f.InstituteID == nil || *f.InstituteID == "" {
		return errors.New("institute ID cannot be empty")
	}
	return nil
}
