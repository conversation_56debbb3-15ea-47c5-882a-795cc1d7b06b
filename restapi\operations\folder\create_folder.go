// Code generated by go-swagger; DO NOT EDIT.

package folder

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"context"
	"net/http"

	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// CreateFolderHandlerFunc turns a function with the right signature into a create folder handler
type CreateFolderHandlerFunc func(CreateFolderParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn CreateFolderHandlerFunc) Handle(params CreateFolderParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// CreateFolderHandler interface for that can handle valid create folder params
type CreateFolderHandler interface {
	Handle(CreateFolderParams, interface{}) middleware.Responder
}

// NewCreateFolder creates a new http.Handler for the create folder operation
func NewCreateFolder(ctx *middleware.Context, handler <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) *CreateFolder {
	return &CreateFolder{Context: ctx, Handler: handler}
}

/*
	CreateFolder swagger:route POST /institute/{instituteId}/folder folder createFolder

# Create a new folder

Create a folder under an institute
*/
type CreateFolder struct {
	Context *middleware.Context
	Handler CreateFolderHandler
}

func (o *CreateFolder) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewCreateFolderParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}

// CreateFolderBody create folder body
//
// swagger:model CreateFolderBody
type CreateFolderBody struct {

	// name
	// Example: Chapter 1 Assignments
	Name string `json:"name,omitempty"`
}

// Validate validates this create folder body
func (o *CreateFolderBody) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this create folder body based on context it is used
func (o *CreateFolderBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (o *CreateFolderBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *CreateFolderBody) UnmarshalBinary(b []byte) error {
	var res CreateFolderBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}
