package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/assignment"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type createAssignmentImpl struct {
	assignmentProvider data_providers.AssignmentProvider
	instituteProvider  data_providers.InstituteProvider
	termProvider       data_providers.TermProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewCreateAssignmentHandler(
	assignmentProvider data_providers.AssignmentProvider,
	instituteProvider data_providers.InstituteProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) assignment.CreateAssignmentHandler {
	return &createAssignmentImpl{
		assignmentProvider: assignmentProvider,
		instituteProvider:  instituteProvider,
		termProvider:       termProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *createAssignmentImpl) Handle(params assignment.CreateAssignmentParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : CreateAssignmentHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return assignment.NewCreateAssignmentForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters (using a custom validator similar to your institute validator).
	valid, validateResp := impl.createAssignmentValidator(params)
	if !valid {
		return validateResp
	}
	createdBy := principal.(string)

	termId := constants.EmptyString
	if params.TermID != nil {
		term, err := impl.termProvider.Get(ctx, *params.TermID, params.InstituteID)
		if err != nil {
			log.Error().Msg(err.Error())
		}
		if term != nil {
			termId = *term.ID
		}
	} else {
		currentTerm, err := impl.termProvider.GetCurrent(ctx, params.InstituteID)
		if err != (nil) {
			log.Error().Msg(err.Error())
		}
		if currentTerm != nil {
			termId = *currentTerm.ID
		}
	}
	if termId == constants.EmptyString {
		return assignment.NewCreateAssignmentInternalServerError().WithPayload("Unable to fetch Term")
	}

	sections := params.Assignment.SectionList
	questions := []entities.Question{}
	for _, question := range params.Assignment.Questions {
		topics := []entities.Topic{}
		for _, topic := range question.Topics {
			topics = append(topics, *entities.NewTopic(topic.Chapter, topic.Topics))
		}
		questions = append(questions, *entities.NewQuestion(int(question.QuestionNumber), question.Question, question.QuestionRubric, float32(question.QuestionScore), topics))
	}

	// Handle folderId - convert empty string to nil pointer
	var folderId *string
	if params.Assignment.FolderID != "" {
		folderId = &params.Assignment.FolderID
	}

	assignmentEntity := entities.NewAssignment(params.InstituteID, params.Assignment.Name, termId, params.Assignment.SubjectName, int(params.Assignment.Class), sections, nil, float32(params.Assignment.TotalScore), questions, createdBy, folderId)
	err = assignmentEntity.AddStatusHistory(constants.AssignmentHistoryStatusCreated, createdBy)
	if err != nil {
		log.Error().Msg(err.Error())
		return assignment.NewCreateAssignmentInternalServerError().WithPayload("Unable to create Assignment")
	}

	err = assignmentEntity.Validate()
	if err != nil {
		log.Error().Msg(err.Error())
		return assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Parameters")
	}

	assignmentIDResp, err := impl.assignmentProvider.Add(ctx, assignmentEntity)
	if err != nil {
		log.Error().Msg(err.Error())
		return assignment.NewCreateAssignmentInternalServerError().WithPayload("Unable to create Assignment")
	}

	return assignment.NewCreateAssignmentOK().WithPayload(
		&models.SuccessResponse{
			ID:      assignmentIDResp,
			Message: "Successfully created Assignment",
		},
	)
}

func (impl *createAssignmentImpl) createAssignmentValidator(params assignment.CreateAssignmentParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "createAssignmentValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, assignment.NewCreateAssignmentInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.Assignment == nil {
		return false, assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Assignment Parameters")
	}
	if params.Assignment.Class == 0 {
		return false, assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Class")
	}
	if len(params.Assignment.SectionList) <= 0 {
		return false, assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Section")
	}
	if params.Assignment.SubjectName == constants.EmptyString {
		return false, assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Subject")
	}
	if params.Assignment.Name == constants.EmptyString {
		return false, assignment.NewCreateAssignmentBadRequest().WithPayload("Invalid Name")
	}
	return true, nil
}
